// dataExport/index.js
// 数据导出云函数 - 独立实现，不影响现有功能

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 数据库集合配置
const COLLECTIONS_CONFIG = {
  // 核心业务数据
  users: {
    name: '用户信息',
    sensitiveFields: ['openid'],
    timeField: 'createTime'
  },
  courses: {
    name: '活动信息', 
    sensitiveFields: [],
    timeField: 'createTime'
  },
  bookings: {
    name: '预约记录',
    sensitiveFields: ['userId'],
    timeField: 'createTime'
  },
  membershipCard: {
    name: '会员卡信息',
    sensitiveFields: ['userId'],
    timeField: 'issueDate'
  },
  membershipCardTemplate: {
    name: '会员卡模板',
    sensitiveFields: [],
    timeField: 'createTime'
  },
  coursesTemplate: {
    name: '活动模板',
    sensitiveFields: [],
    timeField: 'createTime'
  },
  systemSettings: {
    name: '系统设置',
    sensitiveFields: ['phone'],
    timeField: 'updatedAt'
  },
  notifications: {
    name: '通知消息',
    sensitiveFields: ['userId'],
    timeField: 'createTime'
  },
  album_images: {
    name: '相册图片',
    sensitiveFields: [],
    timeField: 'createTime'
  },
  album_folders: {
    name: '相册文件夹',
    sensitiveFields: [],
    timeField: 'createTime'
  }
};

/**
 * 权限验证函数 - 复用现有逻辑
 */
async function verifyAdmin(openid) {
  try {
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    const isAdmin = user.roles && user.roles.includes('管理员');
    
    if (!isAdmin) {
      return {
        success: false,
        message: '权限不足，只有管理员可以执行此操作'
      };
    }
    
    return {
      success: true,
      user: user
    };
  } catch (error) {
    return {
      success: false,
      message: '权限验证失败',
      error: error.message
    };
  }
}

/**
 * 记录导出操作日志
 */
async function logExportOperation(operatorId, operation, parameters, result) {
  try {
    await db.collection('export_logs').add({
      data: {
        operatorId,
        operation,
        parameters,
        result: result.success ? 'success' : 'failed',
        error: result.error || null,
        fileSize: result.fileSize || null,
        recordCount: result.recordCount || null,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('记录导出日志失败:', error);
  }
}

/**
 * 分批查询集合数据
 */
async function exportCollection(collectionName, options = {}) {
  const { 
    dateRange = null, 
    batchSize = 100,
    privacyMode = false 
  } = options;
  
  const allData = [];
  let hasMore = true;
  let lastId = null;
  
  console.log(`开始导出集合: ${collectionName}`);
  
  while (hasMore) {
    let query = db.collection(collectionName)
      .orderBy('_id', 'asc')
      .limit(batchSize);
    
    // 分页查询
    if (lastId) {
      query = query.where({
        _id: _.gt(lastId)
      });
    }
    
    // 时间范围过滤
    if (dateRange && dateRange.start && dateRange.end) {
      const config = COLLECTIONS_CONFIG[collectionName];
      const timeField = config?.timeField || 'createTime';
      
      const timeCondition = {
        [timeField]: _.and(
          _.gte(new Date(dateRange.start)),
          _.lte(new Date(dateRange.end))
        )
      };
      
      if (lastId) {
        query = query.where(_.and([
          { _id: _.gt(lastId) },
          timeCondition
        ]));
      } else {
        query = query.where(timeCondition);
      }
    }
    
    const result = await query.get();
    const data = result.data;
    
    if (data.length === 0) {
      hasMore = false;
      break;
    }
    
    // 数据脱敏处理
    const processedData = privacyMode ? 
      data.map(item => maskSensitiveData(item, collectionName)) : 
      data;
    
    allData.push(...processedData);
    lastId = data[data.length - 1]._id;
    
    // 如果本批次少于batchSize，说明已经是最后一批
    if (data.length < batchSize) {
      hasMore = false;
    }
    
    console.log(`集合 ${collectionName} 已导出 ${allData.length} 条记录`);
  }
  
  return allData;
}

/**
 * 敏感数据脱敏处理
 */
function maskSensitiveData(item, collectionName) {
  const config = COLLECTIONS_CONFIG[collectionName];
  if (!config || !config.sensitiveFields) {
    return item;
  }
  
  const maskedItem = { ...item };
  
  config.sensitiveFields.forEach(field => {
    if (maskedItem[field]) {
      if (field === 'openid') {
        // openid只保留后4位
        maskedItem[field] = '****' + maskedItem[field].slice(-4);
      } else if (field === 'phone') {
        // 手机号中间4位脱敏
        maskedItem[field] = maskedItem[field].replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
      } else {
        // 其他字段用星号替换
        maskedItem[field] = '****';
      }
    }
  });
  
  return maskedItem;
}

/**
 * 云函数入口
 */
exports.main = async (event, context) => {
  const { action, data = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  
  console.log('数据导出云函数被调用:', { action, openid });
  
  // 验证管理员权限
  const authResult = await verifyAdmin(openid);
  if (!authResult.success) {
    return authResult;
  }
  
  try {
    let result;
    
    switch (action) {
      case 'exportFullDatabase':
        result = await exportFullDatabase(data);
        break;
      case 'exportSelectedCollections':
        result = await exportSelectedCollections(data);
        break;
      case 'exportByDateRange':
        result = await exportByDateRange(data);
        break;
      case 'getCollectionsList':
        result = await getCollectionsList();
        break;
      case 'getExportStatistics':
        result = await getExportStatistics();
        break;
      default:
        result = {
          success: false,
          message: '未知操作类型'
        };
    }
    
    // 记录操作日志
    await logExportOperation(openid, action, data, result);
    
    return result;
    
  } catch (error) {
    console.error('数据导出失败:', error);
    const errorResult = {
      success: false,
      message: '数据导出失败',
      error: error.message
    };
    
    // 记录错误日志
    await logExportOperation(openid, action, data, errorResult);
    
    return errorResult;
  }
};

/**
 * 导出全部数据库
 */
async function exportFullDatabase(options = {}) {
  const { 
    format = 'json',
    includeMetadata = true,
    privacyMode = false,
    compression = false
  } = options;
  
  const exportData = {
    exportTime: new Date().toISOString(),
    version: '1.0',
    format: format,
    metadata: includeMetadata ? {
      totalCollections: Object.keys(COLLECTIONS_CONFIG).length,
      privacyMode: privacyMode,
      compression: compression
    } : null,
    collections: {}
  };
  
  let totalRecords = 0;
  
  // 逐个导出集合
  for (const [collectionName, config] of Object.entries(COLLECTIONS_CONFIG)) {
    try {
      console.log(`正在导出集合: ${collectionName} (${config.name})`);
      
      const collectionData = await exportCollection(collectionName, {
        privacyMode: privacyMode
      });
      
      exportData.collections[collectionName] = {
        name: config.name,
        count: collectionData.length,
        data: collectionData
      };
      
      totalRecords += collectionData.length;
      
      console.log(`集合 ${collectionName} 导出完成，共 ${collectionData.length} 条记录`);
      
    } catch (error) {
      console.error(`导出集合 ${collectionName} 失败:`, error);
      exportData.collections[collectionName] = {
        name: config.name,
        error: error.message,
        count: 0,
        data: []
      };
    }
  }
  
  // 生成文件名
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
  const fileName = `yoga_house_full_backup_${timestamp}.${format}`;
  
  // 计算文件大小（估算）
  const jsonString = JSON.stringify(exportData);
  const fileSizeKB = Math.round(jsonString.length / 1024);
  
  return {
    success: true,
    message: '全量数据导出成功',
    data: {
      fileName: fileName,
      exportData: exportData,
      totalCollections: Object.keys(COLLECTIONS_CONFIG).length,
      totalRecords: totalRecords,
      fileSize: `${fileSizeKB}KB`,
      exportTime: exportData.exportTime
    },
    recordCount: totalRecords,
    fileSize: `${fileSizeKB}KB`
  };
}

/**
 * 导出选定的集合
 */
async function exportSelectedCollections(options = {}) {
  const {
    collections = [],
    format = 'json',
    includeMetadata = true,
    privacyMode = false,
    dateRange = null
  } = options;

  if (!Array.isArray(collections) || collections.length === 0) {
    return {
      success: false,
      message: '请选择要导出的数据集合'
    };
  }

  // 验证集合名称
  const invalidCollections = collections.filter(name => !COLLECTIONS_CONFIG[name]);
  if (invalidCollections.length > 0) {
    return {
      success: false,
      message: `无效的集合名称: ${invalidCollections.join(', ')}`
    };
  }

  const exportData = {
    exportTime: new Date().toISOString(),
    version: '1.0',
    format: format,
    metadata: includeMetadata ? {
      selectedCollections: collections,
      totalCollections: collections.length,
      privacyMode: privacyMode,
      dateRange: dateRange
    } : null,
    collections: {}
  };

  let totalRecords = 0;

  // 导出选定的集合
  for (const collectionName of collections) {
    try {
      const config = COLLECTIONS_CONFIG[collectionName];
      console.log(`正在导出集合: ${collectionName} (${config.name})`);

      const collectionData = await exportCollection(collectionName, {
        privacyMode: privacyMode,
        dateRange: dateRange
      });

      exportData.collections[collectionName] = {
        name: config.name,
        count: collectionData.length,
        data: collectionData
      };

      totalRecords += collectionData.length;

      console.log(`集合 ${collectionName} 导出完成，共 ${collectionData.length} 条记录`);

    } catch (error) {
      console.error(`导出集合 ${collectionName} 失败:`, error);
      exportData.collections[collectionName] = {
        name: COLLECTIONS_CONFIG[collectionName].name,
        error: error.message,
        count: 0,
        data: []
      };
    }
  }

  // 生成文件名
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
  const collectionsStr = collections.join('_');
  const fileName = `yoga_house_${collectionsStr}_${timestamp}.${format}`;

  // 计算文件大小
  const jsonString = JSON.stringify(exportData);
  const fileSizeKB = Math.round(jsonString.length / 1024);

  return {
    success: true,
    message: '选择性数据导出成功',
    data: {
      fileName: fileName,
      exportData: exportData,
      totalCollections: collections.length,
      totalRecords: totalRecords,
      fileSize: `${fileSizeKB}KB`,
      exportTime: exportData.exportTime
    },
    recordCount: totalRecords,
    fileSize: `${fileSizeKB}KB`
  };
}

/**
 * 按时间范围导出数据
 */
async function exportByDateRange(options = {}) {
  const {
    collections = 'all',
    dateRange = {},
    format = 'json',
    includeMetadata = true,
    privacyMode = false
  } = options;

  if (!dateRange.start || !dateRange.end) {
    return {
      success: false,
      message: '请指定有效的时间范围'
    };
  }

  // 验证时间格式
  const startDate = new Date(dateRange.start);
  const endDate = new Date(dateRange.end);

  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    return {
      success: false,
      message: '时间格式无效，请使用 YYYY-MM-DD 格式'
    };
  }

  if (startDate >= endDate) {
    return {
      success: false,
      message: '开始时间必须早于结束时间'
    };
  }

  // 确定要导出的集合
  const collectionsToExport = collections === 'all' ?
    Object.keys(COLLECTIONS_CONFIG) :
    collections;

  const exportData = {
    exportTime: new Date().toISOString(),
    version: '1.0',
    format: format,
    metadata: includeMetadata ? {
      dateRange: dateRange,
      collections: collectionsToExport,
      totalCollections: collectionsToExport.length,
      privacyMode: privacyMode
    } : null,
    collections: {}
  };

  let totalRecords = 0;

  // 按时间范围导出数据
  for (const collectionName of collectionsToExport) {
    try {
      const config = COLLECTIONS_CONFIG[collectionName];
      console.log(`正在按时间范围导出集合: ${collectionName} (${config.name})`);

      const collectionData = await exportCollection(collectionName, {
        privacyMode: privacyMode,
        dateRange: dateRange
      });

      exportData.collections[collectionName] = {
        name: config.name,
        count: collectionData.length,
        data: collectionData,
        timeField: config.timeField
      };

      totalRecords += collectionData.length;

      console.log(`集合 ${collectionName} 时间范围导出完成，共 ${collectionData.length} 条记录`);

    } catch (error) {
      console.error(`导出集合 ${collectionName} 失败:`, error);
      exportData.collections[collectionName] = {
        name: COLLECTIONS_CONFIG[collectionName].name,
        error: error.message,
        count: 0,
        data: [],
        timeField: COLLECTIONS_CONFIG[collectionName].timeField
      };
    }
  }

  // 生成文件名
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
  const dateStr = `${dateRange.start}_to_${dateRange.end}`;
  const fileName = `yoga_house_range_${dateStr}_${timestamp}.${format}`;

  // 计算文件大小
  const jsonString = JSON.stringify(exportData);
  const fileSizeKB = Math.round(jsonString.length / 1024);

  return {
    success: true,
    message: '时间范围数据导出成功',
    data: {
      fileName: fileName,
      exportData: exportData,
      totalCollections: collectionsToExport.length,
      totalRecords: totalRecords,
      fileSize: `${fileSizeKB}KB`,
      exportTime: exportData.exportTime,
      dateRange: dateRange
    },
    recordCount: totalRecords,
    fileSize: `${fileSizeKB}KB`
  };
}

/**
 * 获取可导出的集合列表
 */
async function getCollectionsList() {
  const collections = Object.entries(COLLECTIONS_CONFIG).map(([key, config]) => ({
    id: key,
    name: config.name,
    sensitiveFields: config.sensitiveFields,
    timeField: config.timeField
  }));

  return {
    success: true,
    message: '获取集合列表成功',
    data: {
      collections: collections,
      total: collections.length
    }
  };
}

/**
 * 获取导出统计信息
 */
async function getExportStatistics() {
  try {
    const stats = {};

    // 统计各集合的记录数量
    for (const [collectionName, config] of Object.entries(COLLECTIONS_CONFIG)) {
      try {
        const countResult = await db.collection(collectionName).count();
        stats[collectionName] = {
          name: config.name,
          count: countResult.total,
          hasTimeField: !!config.timeField
        };
      } catch (error) {
        stats[collectionName] = {
          name: config.name,
          count: 0,
          error: error.message,
          hasTimeField: !!config.timeField
        };
      }
    }

    // 计算总记录数
    const totalRecords = Object.values(stats).reduce((sum, stat) => sum + (stat.count || 0), 0);

    return {
      success: true,
      message: '获取导出统计成功',
      data: {
        collections: stats,
        totalCollections: Object.keys(COLLECTIONS_CONFIG).length,
        totalRecords: totalRecords,
        lastUpdated: new Date().toISOString()
      }
    };

  } catch (error) {
    return {
      success: false,
      message: '获取导出统计失败',
      error: error.message
    };
  }
}
