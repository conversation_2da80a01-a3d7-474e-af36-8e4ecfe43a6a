# 瑜伽馆数据导出功能使用指南

## 📋 功能概述

数据导出功能为瑜伽馆管理系统提供了完整的数据备份和导出能力，支持管理员将系统中的所有数据导出为JSON格式文件，用于数据备份、迁移或分析。

## 🚀 快速开始

### 1. 部署云函数

首先需要部署独立的数据导出云函数：

```bash
# 在微信开发者工具中
1. 右键点击 cloudfunctions/dataExport 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成
```

### 2. 访问导出功能

1. 使用管理员账号登录小程序
2. 进入"我的" → "系统设置"页面
3. 找到"数据维护"区域
4. 点击"导出数据备份"按钮

## 📊 导出功能详解

### 导出范围选项

#### 1. 全部数据
- **描述**：导出所有数据库集合的完整数据
- **适用场景**：完整系统备份、数据迁移
- **包含集合**：
  - users（用户信息）
  - courses（活动信息）
  - bookings（预约记录）
  - membershipCard（会员卡信息）
  - membershipCardTemplate（会员卡模板）
  - coursesTemplate（活动模板）
  - systemSettings（系统设置）
  - notifications（通知消息）
  - album_images（相册图片）
  - album_folders（相册文件夹）

#### 2. 选择集合
- **描述**：选择特定的数据集合进行导出
- **适用场景**：部分数据备份、特定数据分析
- **操作方式**：在弹窗中勾选需要导出的集合

#### 3. 时间范围
- **描述**：按时间范围过滤数据进行导出
- **适用场景**：增量备份、特定时期数据分析
- **时间字段**：根据各集合的时间字段（createTime/updateTime）进行过滤
- **限制**：时间范围不能超过1年

### 导出选项

#### 包含元数据
- **开启**：导出文件包含统计信息、配置参数等元数据
- **关闭**：仅导出纯数据，文件更小

#### 隐私保护模式
- **开启**：敏感数据进行脱敏处理
  - openid：只保留后4位，前面用****替换
  - 手机号：中间4位用****替换
  - 其他敏感字段：用****替换
- **关闭**：导出原始数据

## 📁 导出文件格式

### 文件命名规则

```
yoga_house_[类型]_[时间戳].json

示例：
- yoga_house_full_backup_2024-08-20T10-30-00.json（全量备份）
- yoga_house_users_courses_2024-08-20T10-30-00.json（选择性导出）
- yoga_house_range_2024-08-01_to_2024-08-20_2024-08-20T10-30-00.json（时间范围）
```

### 文件结构

```json
{
  "exportTime": "2024-08-20T10:30:00.000Z",
  "version": "1.0",
  "format": "json",
  "metadata": {
    "totalCollections": 10,
    "totalRecords": 1250,
    "privacyMode": false,
    "compression": false
  },
  "collections": {
    "users": {
      "name": "用户信息",
      "count": 150,
      "data": [
        {
          "_id": "user_id_1",
          "openid": "****abcd",
          "nickName": "张三",
          "roles": ["学员"],
          "createTime": "2024-08-01T00:00:00.000Z"
        }
      ]
    },
    "courses": {
      "name": "活动信息",
      "count": 50,
      "data": [...]
    }
  }
}
```

## 🔧 使用场景

### 1. 定期备份
- **频率**：建议每周进行一次全量备份
- **配置**：全部数据 + 包含元数据 + 隐私保护模式
- **存储**：将导出文件保存到安全的位置

### 2. 数据迁移
- **场景**：系统升级、环境迁移
- **配置**：全部数据 + 包含元数据 + 关闭隐私保护
- **注意**：确保目标环境兼容数据格式

### 3. 数据分析
- **场景**：业务分析、报表生成
- **配置**：选择集合 + 时间范围 + 隐私保护模式
- **工具**：可使用提供的查看器工具分析数据

### 4. 合规审计
- **场景**：数据合规检查、审计要求
- **配置**：全部数据 + 包含元数据 + 隐私保护模式
- **文档**：保留导出记录和操作日志

## 🛠️ 数据查看工具

项目提供了一个HTML查看器工具，用于查看和分析导出的数据：

### 使用方法
1. 打开 `test/export-viewer.html` 文件
2. 将导出的JSON文件拖拽到页面中
3. 查看数据统计和详细内容

### 功能特性
- 📊 元数据展示：导出时间、记录数量等
- 📚 集合浏览：按集合分类查看数据
- 📋 表格展示：以表格形式展示数据记录
- 🔍 数据预览：显示每个集合的前10条记录

## ⚠️ 注意事项

### 安全考虑
1. **权限控制**：只有管理员可以执行导出操作
2. **数据敏感性**：导出文件包含敏感信息，需妥善保管
3. **访问日志**：所有导出操作都会记录日志
4. **文件清理**：定期清理临时导出文件

### 性能限制
1. **文件大小**：大型数据库可能产生较大的导出文件
2. **导出时间**：数据量大时导出可能需要较长时间
3. **内存使用**：导出过程中会占用一定的云函数内存
4. **并发限制**：同时只能进行一个导出任务

### 故障排除
1. **权限错误**：确认当前用户具有管理员权限
2. **导出失败**：检查网络连接和云函数状态
3. **文件损坏**：重新导出或检查导出参数
4. **时间范围错误**：确认开始时间早于结束时间

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 检查控制台错误日志
2. 确认云函数部署状态
3. 验证用户权限设置
4. 联系技术支持团队

## 🔄 版本更新

### v1.0.0（当前版本）
- ✅ 基础导出功能
- ✅ 三种导出范围选项
- ✅ 隐私保护模式
- ✅ 数据统计功能
- ✅ HTML查看器工具

### 计划功能
- 🔄 数据压缩和加密
- 🔄 定时自动备份
- 🔄 多格式导出（CSV、Excel）
- 🔄 增量备份功能
- 🔄 云存储集成
