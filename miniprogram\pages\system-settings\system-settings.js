// system-settings.js
// 系统设置页面逻辑文件
// 这是小程序的系统设置页面，负责管理系统全局配置、联系信息、业务规则等功能
// 类似于Web应用的后台管理页面或移动应用的系统配置页面

/**
 * 模块导入说明
 *
 * 系统设置页面的特点：
 * 1. 权限控制：只有管理员可以访问和修改
 * 2. 全局配置：影响整个小程序的业务逻辑
 * 3. 实时生效：设置修改后立即在全系统生效
 * 4. 数据持久化：所有设置保存到云数据库
 *
 * 安全考虑：
 * - 严格的权限验证
 * - 数据验证和格式化
 * - 操作日志记录
 * - 错误处理和回滚
 */

// 导入Toast工具函数，用于操作反馈
import { showToast, showLoading, hideToast } from '../../utils/toast.js';

// 导入系统设置工具函数，用于多条公告管理
import { updateAnnouncements } from '../../utils/systemSettings.js';

/**
 * 获取全局应用实例
 *
 * getApp()函数：
 * - 获取小程序的全局App实例
 * - 可以访问全局数据和方法
 * - 用于权限验证、用户信息获取等
 *
 * 与其他技术对比：
 * - Android: getApplication()
 * - iOS: UIApplication.shared
 * - Web: window对象
 */
const app = getApp()

/**
 * Page()函数：注册系统设置页面
 *
 * 页面功能：
 * 1. 预约规则设置：取消预约时间限制等业务规则
 * 2. 系统状态设置：维护模式开关
 * 3. 联系信息管理：电话、地址、公告等信息
 * 4. 权限控制：确保只有管理员可以操作
 * 5. 数据同步：与云数据库实时同步
 *
 * 设计原则：
 * - 权限优先：所有操作都需要管理员权限
 * - 数据验证：输入数据的格式验证和范围检查
 * - 用户友好：清晰的操作反馈和错误提示
 * - 系统稳定：异常处理和数据回滚机制
 */
Page({
  /**
   * data: 页面数据对象
   *
   * 数据分类：
   * 1. 业务设置：影响系统业务逻辑的配置
   * 2. 系统状态：系统运行状态的控制
   * 3. 信息管理：对外展示的信息内容
   * 4. UI状态：页面交互状态管理
   * 5. 权限控制：用户权限验证结果
   */
  data: {
    /**
     * 预约取消时间设置
     *
     * 业务规则：
     * 用户在课程开始前多长时间内不能取消预约
     * 这个设置影响整个小程序的预约取消逻辑
     *
     * 数据格式：
     * - cancelTimeLimit: 以分钟为单位的数值
     * - formattedTime: 用户友好的显示格式
     * - timeUnit: 时间单位（小时/分钟）
     */

    // 取消预约时间限制（分钟）
    // 默认180分钟（3小时），用户在课程开始前3小时内不能取消预约
    cancelTimeLimit: 180,

    // 格式化显示的时间
    // 将分钟数转换为用户友好的显示格式，如"3"（配合timeUnit显示为"3小时"）
    formattedTime: '3',

    // 时间单位
    // 显示用的时间单位，"小时"或"分钟"
    timeUnit: '小时',

    /**
     * 维护模式设置
     *
     * 系统维护功能：
     * 当系统需要维护时，可以开启维护模式
     * 开启后，普通用户将无法使用预约等核心功能
     * 只有管理员可以正常使用系统
     */

    // 维护模式开关
    // 布尔值，true表示系统处于维护状态
    maintenanceMode: false,

    /**
     * 联系信息设置
     *
     * 对外展示信息：
     * 这些信息会在首页、个人中心等地方展示给用户
     * 用于用户联系门店或了解相关信息
     */

    // 联系电话
    // 字符串类型，门店的联系电话号码
    contactPhone: '',

    // 联系地址
    // 字符串类型，门店的详细地址信息
    contactAddress: '',

    // 联系公告（兼容旧版本）
    // 字符串类型，向用户展示的重要公告或通知
    contactAnnouncement: '',

    // 多条公告数组（新功能）
    // 数组类型，包含多条公告的详细信息
    announcements: [],

    /**
     * 权限控制
     *
     * 安全验证：
     * 确保只有管理员可以访问和修改系统设置
     */

    // 管理员权限标识
    // 布尔值，true表示当前用户具有管理员权限
    isAdmin: false,

    /**
     * 数据导出功能相关数据
     */

    // 导出配置弹窗显示状态
    showExportDialog: false,

    // 导出进度弹窗显示状态
    showExportProgress: false,

    // 数据统计弹窗显示状态
    showStatisticsDialog: false,

    // 导出范围选择：'all' | 'selected' | 'dateRange'
    exportScope: 'all',

    // 选中的数据集合（当exportScope为'selected'时使用）
    selectedCollections: [],

    // 可用的数据集合列表
    availableCollections: [],

    // 时间范围选择
    startDate: '',
    endDate: '',

    // 导出选项：['includeMetadata', 'privacyMode']
    exportOptions: ['includeMetadata'],

    // 导出状态文本
    exportStatusText: '正在准备导出...',

    // 导出进度详情
    exportProgressDetail: '',

    // 数据统计信息
    statistics: {
      totalCollections: 0,
      totalRecords: 0,
      collections: {}
    }
  },

  onLoad(options) {
    this.checkAdminPermission()
    this.loadSettings()
    this.loadAvailableCollections()
  },

  /**
   * formatTimeDisplay: 格式化时间显示的工具方法
   *
   * 功能说明：
   * 将分钟数转换为用户友好的显示格式
   * 自动选择合适的时间单位（小时或分钟）
   *
   * @param {number} minutes - 分钟数
   * @returns {Object} - 包含格式化时间和单位的对象
   *   - formattedTime: 格式化后的时间数字
   *   - timeUnit: 时间单位（'小时' 或 '分钟'）
   *
   * 格式化规则：
   * 1. 整小时：180分钟 → "3小时"
   * 2. 小数小时：150分钟 → "2.5小时"
   * 3. 不足1小时：30分钟 → "30分钟"
   * 4. 无效值：返回默认值"180分钟"
   *
   * 用户体验：
   * 用户更容易理解"3小时"而不是"180分钟"
   * 但对于不规整的时间，仍保持精确性
   */
  formatTimeDisplay(minutes) {
    /**
     * 输入验证和默认值处理
     *
     * 处理情况：
     * - null: 数据库中可能存储的空值
     * - undefined: 未设置的情况
     * - NaN: 非数字输入
     *
     * 默认值选择：
     * 180分钟（3小时）是常见的预约取消时间限制
     */
    if (minutes === null || minutes === undefined || isNaN(minutes)) {
      return {
        formattedTime: '180',
        timeUnit: '分钟'
      }
    }

    /**
     * 时间单位转换逻辑
     *
     * 转换规则：
     * >= 60分钟：转换为小时显示
     * < 60分钟：保持分钟显示
     */
    if (minutes >= 60) {
      // 计算小时数（可能包含小数）
      const hours = minutes / 60

      /**
       * 判断是否为整小时
       *
       * Math.floor(hours) === hours：
       * 如果小时数的整数部分等于原值，说明是整小时
       * 例如：3.0 === 3（整小时），2.5 !== 2（非整小时）
       */
      if (hours === Math.floor(hours)) {
        // 整小时显示
        return {
          formattedTime: hours.toString(),
          timeUnit: '小时'
        }
      } else {
        // 小数小时显示（保留1位小数）
        return {
          formattedTime: hours.toFixed(1),
          timeUnit: '小时'
        }
      }
    } else {
      // 不足60分钟，直接显示分钟数
      return {
        formattedTime: minutes.toString(),
        timeUnit: '分钟'
      }
    }
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadSettings()
  },

  // 检查管理员权限
  async checkAdminPermission() {
    try {
      const userInfo = wx.getStorageSync('userInfo')
      if (!userInfo || !userInfo.roles) {
        showToast(this, { message: '请先登录', theme: 'error' });
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
        return
      }

      const isAdmin = userInfo.roles.includes('管理员')
      this.setData({ isAdmin })

      if (!isAdmin) {
        showToast(this, { message: '权限不足，仅管理员可访问', theme: 'error' });
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('权限检查失败:', error)
      showToast(this, { message: '权限检查失败', theme: 'error' });
    }
  },

  /**
   * loadSettings: 加载系统设置的异步方法
   *
   * 功能说明：
   * 从云函数获取系统设置数据并更新页面显示
   * 使用云函数而不是直接查询数据库，确保数据安全性
   *
   * 数据流程：
   * 1. 显示加载提示
   * 2. 调用云函数获取设置
   * 3. 格式化数据并更新页面
   * 4. 隐藏加载提示
   * 5. 错误处理和用户反馈
   *
   * 安全考虑：
   * 通过云函数访问数据，可以在服务端进行权限验证
   * 避免客户端直接访问敏感的系统设置数据
   */
  async loadSettings() {
    try {
      // 显示加载提示，提升用户体验
      showLoading(this, '加载中...');

      /**
       * 调用云函数获取系统设置
       *
       * 云函数优势：
       * 1. 服务端权限验证：确保只有管理员可以获取设置
       * 2. 数据安全：敏感设置不直接暴露给客户端
       * 3. 业务逻辑封装：复杂的数据处理在服务端完成
       * 4. 统一接口：提供标准化的数据访问接口
       */
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',        // 云函数名称
        data: {
          action: 'getSystemSettings'   // 操作类型
        }
      })

      /**
       * 处理云函数返回结果
       *
       * 返回格式：
       * {
       *   result: {
       *     success: boolean,
       *     data: {
       *       booking: { cancelTimeLimitMinutes: number },
       *       maintenance: { enabled: boolean },
       *       contact: { phone: string, address: string, announcement: string }
       *     }
       *   }
       * }
       */
      if (result.result.success) {
        const settings = result.result.data

        /**
         * 提取和处理取消时间限制
         *
         * 可选链操作符(?.)：
         * settings.booking?.cancelTimeLimitMinutes
         * 安全地访问嵌套属性，如果booking不存在，返回undefined
         */
        const cancelTimeLimit = settings.booking?.cancelTimeLimitMinutes

        // 格式化时间显示
        const timeDisplay = this.formatTimeDisplay(cancelTimeLimit)

        /**
         * 批量更新页面数据
         *
         * 数据处理策略：
         * 1. 使用默认值：|| 操作符提供合理的默认值
         * 2. 可选链访问：?.操作符安全访问嵌套属性
         * 3. 格式化处理：时间数据经过格式化处理
         *
         * 默认值设计：
         * - cancelTimeLimit: 180分钟（3小时）
         * - maintenanceMode: false（非维护状态）
         * - 联系信息: 空字符串（用户可以填写）
         */
        // 处理多条公告数据
        const announcements = settings.contact?.announcements || [];
        const processedAnnouncements = announcements.map(item => ({
          ...item,
          updateTime: this.formatDateTime(item.updateTime || item.createTime)
        }));

        this.setData({
          cancelTimeLimit: cancelTimeLimit || 180,              // 取消时间限制（分钟）
          formattedTime: timeDisplay.formattedTime,             // 格式化时间数字
          timeUnit: timeDisplay.timeUnit,                       // 时间单位
          maintenanceMode: settings.maintenance?.enabled || false, // 维护模式开关
          contactPhone: settings.contact?.phone || '',          // 联系电话
          contactAddress: settings.contact?.address || '',      // 联系地址
          contactAnnouncement: settings.contact?.announcement || '', // 联系公告（兼容）
          announcements: processedAnnouncements                 // 多条公告数组
        })
      } else {
        // 云函数执行失败的处理
        showToast(this, { message: '获取系统设置失败', theme: 'error' });
      }

      // 隐藏加载提示
      hideToast(this);
    } catch (error) {
      /**
       * 异常处理
       *
       * 可能的异常：
       * 1. 网络错误：网络连接问题
       * 2. 云函数错误：服务端处理异常
       * 3. 权限错误：用户权限不足
       * 4. 数据格式错误：返回数据格式异常
       *
       * 处理策略：
       * 1. 记录错误日志：便于调试和问题排查
       * 2. 隐藏加载提示：恢复页面正常状态
       * 3. 显示错误提示：告知用户操作失败
       */
      console.error('加载设置失败:', error)
      hideToast(this);
      showToast(this, { message: '加载设置失败', theme: 'error' });
    }
  },

  // 时间输入改变
  onCancelTimeChange(e) {
    const value = e.detail.value
    const timeDisplay = this.formatTimeDisplay(value)
    this.setData({
      cancelTimeLimit: value,
      formattedTime: timeDisplay.formattedTime,
      timeUnit: timeDisplay.timeUnit
    })
  },

  // 时间输入失焦验证并保存
  async onCancelTimeBlur(e) {
    const value = parseInt(e.detail.value)
    if (isNaN(value) || value < 1 || value > 1440) {
      showToast(this, { message: '请输入1-1440之间的数字（分钟）', theme: 'error' });
      return
    }

    const timeDisplay = this.formatTimeDisplay(value)

    try {
      // 显示保存中状态
      showLoading(this, '保存中...');

      // 调用云函数保存到数据库
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'updateSystemSettings',
          data: {
            booking: {
              cancelTimeLimitMinutes: value
            }
          }
        }
      });

      hideToast(this);

      if (result.result.success) {
        // 更新本地数据
        this.setData({
          cancelTimeLimit: value,
          formattedTime: timeDisplay.formattedTime,
          timeUnit: timeDisplay.timeUnit
        });

        showToast(this, { message: '取消时间设置保存成功', theme: 'success' });
      } else {
        showToast(this, {
          message: result.result.message || '保存失败',
          theme: 'error'
        });
      }
    } catch (error) {
      console.error('保存取消时间设置失败:', error);
      hideToast(this);
      showToast(this, { message: '保存失败', theme: 'error' });
    }
  },

  // 联系电话输入改变
  onContactPhoneChange(e) {
    this.setData({
      contactPhone: e.detail.value
    })
  },

  // 联系电话失焦保存
  async onContactPhoneBlur(e) {
    const phone = e.detail.value.trim();

    try {
      // 显示保存中状态
      showLoading(this, '保存中...');

      // 调用云函数保存到数据库
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'updateSystemSettings',
          data: {
            contact: {
              phone: phone,
              address: this.data.contactAddress,
              announcement: this.data.contactAnnouncement,
              announcements: this.data.announcements.map(item => ({
                id: item.id,
                content: item.content,
                createTime: item.createTime,
                updateTime: item.updateTime,
                order: item.order,
                isActive: item.isActive
              }))
            }
          }
        }
      });

      hideToast(this);

      if (result.result.success) {
        // 更新本地数据
        this.setData({
          contactPhone: phone
        });

        showToast(this, { message: '联系电话保存成功', theme: 'success' });
      } else {
        showToast(this, {
          message: result.result.message || '保存失败',
          theme: 'error'
        });
      }
    } catch (error) {
      console.error('保存联系电话失败:', error);
      hideToast(this);
      showToast(this, { message: '保存失败', theme: 'error' });
    }
  },

  // 门店地址输入改变
  onContactAddressChange(e) {
    this.setData({
      contactAddress: e.detail.value
    })
  },

  // 门店地址失焦保存
  async onContactAddressBlur(e) {
    const address = e.detail.value.trim();

    try {
      // 显示保存中状态
      showLoading(this, '保存中...');

      // 调用云函数保存到数据库
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'updateSystemSettings',
          data: {
            contact: {
              phone: this.data.contactPhone,
              address: address,
              announcement: this.data.contactAnnouncement,
              announcements: this.data.announcements.map(item => ({
                id: item.id,
                content: item.content,
                createTime: item.createTime,
                updateTime: item.updateTime,
                order: item.order,
                isActive: item.isActive
              }))
            }
          }
        }
      });

      hideToast(this);

      if (result.result.success) {
        // 更新本地数据
        this.setData({
          contactAddress: address
        });

        showToast(this, { message: '门店地址保存成功', theme: 'success' });
      } else {
        showToast(this, {
          message: result.result.message || '保存失败',
          theme: 'error'
        });
      }
    } catch (error) {
      console.error('保存门店地址失败:', error);
      hideToast(this);
      showToast(this, { message: '保存失败', theme: 'error' });
    }
  },



  // ==================== 多条公告处理方法 ====================

  /**
   * 生成UUID
   * @returns {string} UUID字符串
   */
  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  },

  /**
   * 格式化日期时间
   * @param {Date|string} date 日期对象或字符串
   * @returns {string} 格式化后的日期时间字符串
   */
  formatDateTime(date) {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  /**
   * 新增公告
   */
  onAddAnnouncement() {
    // 先退出所有公告的编辑模式
    const currentAnnouncements = this.data.announcements.map(item => ({
      ...item,
      editMode: false
    }));

    const newAnnouncement = {
      id: this.generateUUID(),
      content: '',
      createTime: new Date(),
      updateTime: new Date(),
      order: this.data.announcements.length + 1,
      isActive: true,
      editMode: true, // 新增的公告直接进入编辑模式
      originalContent: '' // 设置原始内容为空
    };

    const announcements = [...currentAnnouncements, newAnnouncement];
    this.setData({
      announcements: announcements.map(item => ({
        ...item,
        updateTime: this.formatDateTime(item.updateTime)
      }))
    });

    showToast(this, {
      message: '已添加新公告，请编辑内容',
      theme: 'success'
    });
  },

  /**
   * 公告内容改变（仅在编辑模式下生效）
   */
  onAnnouncementContentChange(e) {
    const index = e.currentTarget.dataset.index;
    const content = e.detail.value;
    const announcements = [...this.data.announcements];

    if (announcements[index] && announcements[index].editMode) {
      // 只在编辑模式下允许修改内容
      announcements[index].content = content;
      // 注意：这里不更新updateTime，只有确认编辑时才更新

      this.setData({
        announcements
      });
    }
  },

  /**
   * 公告内容失焦自动保存
   */
  async onAnnouncementContentBlur(e) {
    const index = e.currentTarget.dataset.index;
    const content = e.detail.value.trim();
    const announcements = [...this.data.announcements];

    if (!announcements[index] || !announcements[index].editMode) {
      return; // 不在编辑模式下不处理
    }

    // 如果内容没有变化，不需要保存
    if (announcements[index].originalContent === content) {
      return;
    }

    try {
      // 显示保存中状态
      showLoading(this, '保存中...');

      // 准备要保存的公告数据
      const announcementToSave = {
        ...announcements[index],
        content: content,
        editMode: false, // 失焦后退出编辑模式
        updateTime: new Date()
      };

      // 清除原始内容备份
      delete announcementToSave.originalContent;

      // 准备所有公告数据
      const allAnnouncements = announcements.map((item, idx) => {
        if (idx === index) {
          return {
            id: announcementToSave.id,
            content: announcementToSave.content,
            createTime: announcementToSave.createTime,
            updateTime: announcementToSave.updateTime,
            order: announcementToSave.order,
            isActive: announcementToSave.isActive
          };
        }
        return {
          id: item.id,
          content: item.content,
          createTime: item.createTime,
          updateTime: item.updateTime,
          order: item.order,
          isActive: item.isActive
        };
      });

      // 调用云函数保存到数据库
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'updateSystemSettings',
          data: {
            contact: {
              phone: this.data.contactPhone,
              address: this.data.contactAddress,
              announcement: this.data.contactAnnouncement,
              announcements: allAnnouncements
            }
          }
        }
      });

      hideToast(this);

      if (result.result.success) {
        // 更新本地数据
        announcements[index] = {
          ...announcementToSave,
          updateTime: this.formatDateTime(announcementToSave.updateTime)
        };

        this.setData({
          announcements
        });

        showToast(this, {
          message: '公告保存成功',
          theme: 'success'
        });
      } else {
        showToast(this, {
          message: result.result.message || '保存失败',
          theme: 'error'
        });
      }
    } catch (error) {
      console.error('保存公告失败:', error);
      hideToast(this);
      showToast(this, { message: '保存失败', theme: 'error' });
    }
  },



  /**
   * 编辑公告 - 进入编辑模式
   */
  onEditAnnouncement(e) {
    const index = e.currentTarget.dataset.index;
    console.log('编辑公告, index:', index);

    // 先退出其他公告的编辑模式
    const announcements = this.data.announcements.map((item, idx) => ({
      ...item,
      editMode: idx === index ? true : false, // 只有当前公告进入编辑模式
      originalContent: idx === index ? item.content : item.originalContent // 保存原始内容用于取消操作和失焦对比
    }));

    this.setData({
      announcements
    });
  },

  /**
   * 切换公告状态（启用/禁用）
   */
  onToggleAnnouncementStatus(e) {
    const index = e.currentTarget.dataset.index;
    const announcements = [...this.data.announcements];

    if (announcements[index]) {
      // 切换状态
      announcements[index].isActive = !announcements[index].isActive;
      announcements[index].updateTime = this.formatDateTime(new Date());

      this.setData({
        announcements
      });

      // 显示操作反馈
      const statusText = announcements[index].isActive ? '启用' : '禁用';
      showToast(this, {
        message: `公告已${statusText}`,
        theme: 'success'
      });
    }
  },

  /**
   * 确认编辑 - 退出编辑模式（内容已在失焦时自动保存）
   */
  onConfirmEdit(e) {
    const index = e.currentTarget.dataset.index;
    const announcements = [...this.data.announcements];

    if (!announcements[index]) {
      showToast(this, { message: '公告不存在', theme: 'error' });
      return;
    }

    // 直接退出编辑模式，内容已在失焦时保存
    announcements[index].editMode = false;
    announcements[index].updateTime = this.formatDateTime(new Date());
    // 清除原始内容备份
    delete announcements[index].originalContent;

    this.setData({
      announcements
    });

    showToast(this, {
      message: '编辑完成',
      theme: 'success'
    });
  },

  /**
   * 取消编辑
   */
  onCancelEdit(e) {
    const index = e.currentTarget.dataset.index;
    const announcements = [...this.data.announcements];

    if (announcements[index]) {
      // 恢复原始内容
      if (announcements[index].originalContent !== undefined) {
        announcements[index].content = announcements[index].originalContent;
      }

      // 退出编辑模式
      announcements[index].editMode = false;
      // 清除原始内容备份
      delete announcements[index].originalContent;

      this.setData({
        announcements
      });

      showToast(this, {
        message: '已取消编辑',
        theme: 'warning'
      });
    }
  },

  /**
   * 删除公告 - 直接从数据库删除
   */
  onDeleteAnnouncement(e) {
    const index = e.currentTarget.dataset.index;
    const announcements = [...this.data.announcements];
    const announcement = announcements[index];

    if (!announcement) {
      showToast(this, { message: '公告不存在', theme: 'error' });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条公告吗？删除后无法恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            // 显示删除中状态
            showLoading(this, '删除中...');

            // 删除公告
            announcements.splice(index, 1);

            // 重新排序
            announcements.forEach((item, idx) => {
              item.order = idx + 1;
              // 确保删除后其他公告退出编辑模式
              item.editMode = false;
              delete item.originalContent;
            });

            // 准备要保存的公告数据
            const announcementsToSave = announcements.map(item => ({
              id: item.id,
              content: item.content,
              createTime: item.createTime,
              updateTime: item.updateTime,
              order: item.order,
              isActive: item.isActive
            }));

            // 调用云函数保存到数据库
            const result = await wx.cloud.callFunction({
              name: 'adminManagement',
              data: {
                action: 'updateSystemSettings',
                data: {
                  contact: {
                    phone: this.data.contactPhone,
                    address: this.data.contactAddress,
                    announcement: this.data.contactAnnouncement,
                    announcements: announcementsToSave
                  }
                }
              }
            });

            hideToast(this);

            if (result.result.success) {
              // 更新本地数据
              this.setData({
                announcements
              });

              showToast(this, { message: '公告删除成功', theme: 'success' });
            } else {
              showToast(this, {
                message: result.result.message || '删除失败',
                theme: 'error'
              });
            }
          } catch (error) {
            console.error('删除公告失败:', error);
            hideToast(this);
            showToast(this, { message: '删除失败', theme: 'error' });
          }
        }
      }
    });
  },





  // 显示确认对话框
  showConfirmDialog(title, content, onConfirm) {
    const dialog = this.selectComponent('#t-dialog')
    dialog.setData({
      visible: true,
      title: title,
      content: content,
      confirmBtn: '确定',
      cancelBtn: '取消'
    })
    
    dialog.setData({
      onConfirm: () => {
        onConfirm()
        dialog.setData({ visible: false })
      },
      onCancel: () => {
        dialog.setData({ visible: false })
      }
    })
  },



  /**
   * showMessage: 显示消息提示的工具方法
   *
   * 功能说明：
   * 使用TDesign的Message组件显示消息提示
   * 提供统一的消息提示接口
   *
   * @param {string} message - 提示消息内容
   * @param {string} type - 消息类型，默认为'info'
   *   可选值：'info', 'success', 'warning', 'error'
   *
   * 组件特点：
   * - 自动消失：3秒后自动隐藏
   * - 主题样式：根据类型显示不同的颜色和图标
   * - 非阻塞：不会阻止用户的其他操作
   *
   * 使用场景：
   * - 操作成功提示
   * - 警告信息
   * - 错误提示
   * - 一般信息通知
   */
  showMessage(message, type = 'info') {
    // 获取Message组件实例
    const messageComponent = this.selectComponent('#t-message')

    // 显示消息提示
    messageComponent.show({
      message: message,     // 提示内容
      theme: type,          // 主题类型
      duration: 3000        // 显示时长：3秒
    })
  },

  /**
   * onPullDownRefresh: 下拉刷新事件处理
   *
   * 功能说明：
   * 用户下拉页面时触发，重新加载系统设置数据
   * 确保用户看到的是最新的设置信息
   *
   * 实现逻辑：
   * 1. 调用loadSettings()重新加载数据
   * 2. 无论成功还是失败，都要停止下拉刷新动画
   * 3. 使用Promise的then/catch处理异步结果
   *
   * 用户体验：
   * - 提供手动刷新的方式
   * - 确保数据的实时性
   * - 刷新完成后停止loading动画
   *
   * 注意事项：
   * 必须调用wx.stopPullDownRefresh()停止刷新动画
   * 否则页面会一直显示刷新状态
   */
  onPullDownRefresh() {
    // 重新加载设置数据
    this.loadSettings()
      .then(() => {
        // 加载成功，停止下拉刷新动画
        wx.stopPullDownRefresh()
      })
      .catch(() => {
        // 加载失败，也要停止下拉刷新动画
        wx.stopPullDownRefresh()
      })
  },

  // ===================================================================
  // 数据导出功能方法 - Data Export Methods
  // ===================================================================

  /**
   * 加载可用的数据集合列表
   */
  async loadAvailableCollections() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'dataExport',
        data: {
          action: 'getCollectionsList'
        }
      });

      if (result.result.success) {
        this.setData({
          availableCollections: result.result.data.collections
        });
      }
    } catch (error) {
      console.error('加载数据集合列表失败:', error);
    }
  },

  /**
   * 显示导出配置弹窗
   */
  showExportDialog() {
    // 设置默认的时间范围（最近30天）
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    this.setData({
      showExportDialog: true,
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    });
  },

  /**
   * 关闭导出配置弹窗
   */
  closeExportDialog() {
    this.setData({
      showExportDialog: false
    });
  },

  /**
   * 导出范围选择变化
   */
  onExportScopeChange(e) {
    this.setData({
      exportScope: e.detail.value
    });
  },

  /**
   * 选中集合变化
   */
  onSelectedCollectionsChange(e) {
    this.setData({
      selectedCollections: e.detail.value
    });
  },

  /**
   * 开始日期变化
   */
  onStartDateChange(e) {
    this.setData({
      startDate: e.detail.value
    });
  },

  /**
   * 结束日期变化
   */
  onEndDateChange(e) {
    this.setData({
      endDate: e.detail.value
    });
  },

  /**
   * 导出选项变化
   */
  onExportOptionsChange(e) {
    this.setData({
      exportOptions: e.detail.value
    });
  },

  /**
   * 开始导出数据
   */
  async startExport() {
    try {
      // 关闭配置弹窗，显示进度弹窗
      this.setData({
        showExportDialog: false,
        showExportProgress: true,
        exportStatusText: '正在准备导出...',
        exportProgressDetail: '验证权限和参数...'
      });

      // 构建导出参数
      const exportParams = this.buildExportParams();

      // 验证参数
      const validation = this.validateExportParams(exportParams);
      if (!validation.valid) {
        this.setData({
          showExportProgress: false
        });
        showToast(this, { message: validation.message, theme: 'error' });
        return;
      }

      // 更新进度
      this.setData({
        exportStatusText: '正在导出数据...',
        exportProgressDetail: '正在从数据库获取数据，请稍候...'
      });

      // 调用导出云函数
      const result = await this.callExportFunction(exportParams);

      if (result.success) {
        // 导出成功
        this.setData({
          showExportProgress: false
        });

        this.handleExportSuccess(result.data);
      } else {
        // 导出失败
        this.setData({
          showExportProgress: false
        });

        showToast(this, {
          message: result.message || '导出失败',
          theme: 'error'
        });
      }

    } catch (error) {
      console.error('导出数据失败:', error);
      this.setData({
        showExportProgress: false
      });

      showToast(this, {
        message: '导出失败，请重试',
        theme: 'error'
      });
    }
  },

  /**
   * 构建导出参数
   */
  buildExportParams() {
    const { exportScope, selectedCollections, startDate, endDate, exportOptions } = this.data;

    const params = {
      format: 'json',
      includeMetadata: exportOptions.includes('includeMetadata'),
      privacyMode: exportOptions.includes('privacyMode')
    };

    switch (exportScope) {
      case 'all':
        return {
          action: 'exportFullDatabase',
          data: params
        };

      case 'selected':
        return {
          action: 'exportSelectedCollections',
          data: {
            ...params,
            collections: selectedCollections
          }
        };

      case 'dateRange':
        return {
          action: 'exportByDateRange',
          data: {
            ...params,
            collections: 'all',
            dateRange: {
              start: startDate,
              end: endDate
            }
          }
        };

      default:
        return {
          action: 'exportFullDatabase',
          data: params
        };
    }
  },

  /**
   * 验证导出参数
   */
  validateExportParams(params) {
    const { action, data } = params;

    // 验证选择性导出的集合
    if (action === 'exportSelectedCollections') {
      if (!data.collections || data.collections.length === 0) {
        return {
          valid: false,
          message: '请至少选择一个数据集合'
        };
      }
    }

    // 验证时间范围导出的日期
    if (action === 'exportByDateRange') {
      if (!data.dateRange || !data.dateRange.start || !data.dateRange.end) {
        return {
          valid: false,
          message: '请选择有效的时间范围'
        };
      }

      const startDate = new Date(data.dateRange.start);
      const endDate = new Date(data.dateRange.end);

      if (startDate >= endDate) {
        return {
          valid: false,
          message: '开始日期必须早于结束日期'
        };
      }

      // 检查时间范围是否过大（超过1年）
      const oneYear = 365 * 24 * 60 * 60 * 1000;
      if (endDate - startDate > oneYear) {
        return {
          valid: false,
          message: '时间范围不能超过1年'
        };
      }
    }

    return { valid: true };
  },

  /**
   * 调用导出云函数
   */
  async callExportFunction(params) {
    const result = await wx.cloud.callFunction({
      name: 'dataExport',
      data: params
    });

    return result.result;
  },

  /**
   * 处理导出成功
   */
  handleExportSuccess(data) {
    const { fileName, totalRecords, fileSize, exportTime } = data;

    // 将导出数据保存到本地文件
    this.saveExportFile(data);

    // 显示成功提示
    wx.showModal({
      title: '导出成功',
      content: `文件名：${fileName}\n记录数：${totalRecords} 条\n文件大小：${fileSize}\n导出时间：${new Date(exportTime).toLocaleString()}`,
      showCancel: true,
      cancelText: '确定',
      confirmText: '分享文件',
      success: (res) => {
        if (res.confirm) {
          this.shareExportFile(fileName);
        }
      }
    });
  },

  /**
   * 保存导出文件到本地
   */
  saveExportFile(data) {
    try {
      const { fileName, exportData } = data;
      const jsonString = JSON.stringify(exportData, null, 2);

      const fs = wx.getFileSystemManager();
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

      fs.writeFileSync(filePath, jsonString, 'utf8');

      console.log('导出文件已保存到:', filePath);

      // 保存文件路径到data中，用于后续分享
      this.setData({
        lastExportFilePath: filePath,
        lastExportFileName: fileName
      });

    } catch (error) {
      console.error('保存导出文件失败:', error);
      showToast(this, {
        message: '文件保存失败',
        theme: 'warning'
      });
    }
  },

  /**
   * 分享导出文件
   */
  shareExportFile(fileName) {
    const filePath = this.data.lastExportFilePath;

    if (!filePath) {
      showToast(this, {
        message: '文件路径不存在',
        theme: 'error'
      });
      return;
    }

    wx.shareFileMessage({
      filePath: filePath,
      fileName: fileName,
      success: () => {
        showToast(this, {
          message: '文件分享成功',
          theme: 'success'
        });
      },
      fail: (error) => {
        console.error('分享文件失败:', error);
        showToast(this, {
          message: '分享失败，请重试',
          theme: 'error'
        });
      }
    });
  },

  /**
   * 显示数据统计弹窗
   */
  async showExportStatistics() {
    try {
      showLoading(this, '加载统计信息...');

      const result = await wx.cloud.callFunction({
        name: 'dataExport',
        data: {
          action: 'getExportStatistics'
        }
      });

      hideToast(this);

      if (result.result.success) {
        this.setData({
          statistics: result.result.data,
          showStatisticsDialog: true
        });
      } else {
        showToast(this, {
          message: result.result.message || '获取统计信息失败',
          theme: 'error'
        });
      }

    } catch (error) {
      hideToast(this);
      console.error('获取数据统计失败:', error);
      showToast(this, {
        message: '获取统计信息失败',
        theme: 'error'
      });
    }
  },

  /**
   * 关闭数据统计弹窗
   */
  closeStatisticsDialog() {
    this.setData({
      showStatisticsDialog: false
    });
  }

})

/**
 * 文件总结：system-settings.js
 *
 * 这个文件实现了一个完整的系统设置管理页面，主要特点：
 *
 * 1. 权限控制系统：
 *    - 严格的管理员权限验证
 *    - 多重安全检查机制
 *    - 权限不足时的友好提示和强制返回
 *
 * 2. 系统设置管理：
 *    - 预约取消时间限制设置
 *    - 维护模式开关控制
 *    - 联系信息管理（电话、地址、公告）
 *    - 智能的时间格式化显示
 *
 * 3. 数据安全设计：
 *    - 通过云函数访问数据，服务端权限验证
 *    - 敏感操作的二次确认机制
 *    - 完善的错误处理和用户反馈
 *
 * 4. 用户体验优化：
 *    - 智能的时间显示格式（自动选择小时/分钟）
 *    - 实时的输入验证和格式化
 *    - 加载状态提示和错误反馈
 *    - 下拉刷新支持
 *
 * 5. 技术特点：
 *    - 云函数集成：安全的数据访问方式
 *    - 组件化设计：使用TDesign组件库
 *    - 异步处理：完整的async/await异步流程
 *    - 防御性编程：完善的输入验证和错误处理
 *
 * 业务价值：
 *
 * 1. 系统管理：
 *    - 提供完整的系统配置能力
 *    - 支持业务规则的灵活调整
 *    - 维护模式支持系统升级和维护
 *
 * 2. 用户服务：
 *    - 管理对外展示的联系信息
 *    - 支持公告发布和信息更新
 *    - 提升用户服务质量
 *
 * 3. 运营支持：
 *    - 灵活的预约规则设置
 *    - 系统状态的集中管理
 *    - 运营策略的快速调整
 *
 * 与您熟悉的技术对比：
 * - 权限控制：类似于ASP.NET的角色权限系统
 * - 数据访问：类似于WCF服务的安全访问模式
 * - 配置管理：类似于.NET的配置系统
 * - 用户界面：类似于WinForms的设置对话框
 * - 数据验证：类似于数据注解验证
 *
 * 学习价值：
 * 这个文件展示了如何构建一个安全、易用的系统设置管理界面，
 * 包含了权限控制、数据安全、用户体验等多个方面的最佳实践。
 */