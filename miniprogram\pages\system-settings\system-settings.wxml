<!--system-settings.wxml-->
<!-- High-end Redesign -->
<view class="container">
  <!-- 页面内容区域 -->
  <view class="content-wrapper">
    <!-- 预约取消时间设置卡片 -->
  <view class="setting-card">
    <view class="card-header">
      <t-icon name="time" size="24" />
      <text class="card-title">预约取消时间设置</text>
    </view>
    <view class="card-body">
      <view class="setting-item">
        <view class="setting-label">当前设置</view>
        <view class="time-setting-value">
          <text class="time-value">{{formattedTime}}</text>
          <text class="time-unit">{{timeUnit}}</text>
        </view>
      </view>
      <view class="input-row">
        <text class="input-label">活动开始前</text>
        <t-input
          type="number"
          placeholder="分钟"
          value="{{cancelTimeLimit}}"
          bind:change="onCancelTimeChange"
          bind:blur="onCancelTimeBlur"
          class="time-input"
        />
        <text class="input-suffix">分钟内不可取消</text>
      </view>
      <view class="setting-description">
        设置学员可在活动开始前多少分钟取消预约。例如，输入180，则表示活动开始前3小时内无法取消。
      </view>
    </view>
  </view>

  <!-- 联系信息设置卡片 -->
  <view class="setting-card">
    <view class="card-header">
      <t-icon name="call" size="24" />
      <text class="card-title">联系信息设置</text>
    </view>
    <view class="card-body">
      <view class="setting-item">
        <view class="setting-label">联系电话</view>
        <view class="setting-input">
          <t-input
            placeholder="请输入联系电话"
            value="{{contactPhone}}"
            bind:change="onContactPhoneChange"
            bind:blur="onContactPhoneBlur"
            class="contact-input"
            align="right"
          />
        </view>
      </view>
      <view class="setting-item">
        <view class="setting-label">门店地址</view>
        <view class="setting-input">
          <t-input
            placeholder="请输入门店地址"
            value="{{contactAddress}}"
            bind:change="onContactAddressChange"
            bind:blur="onContactAddressBlur"
            class="contact-input"
            align="right"
          />
        </view>
      </view>
      <!-- 多条公告编辑区域 -->
      <view class="setting-item">
        <view class="setting-label">门店公告</view>
        <view class="add-announcement-btn" bind:tap="onAddAnnouncement">
          <t-icon name="add" size="20" />
          <text>新增公告</text>
        </view>
      </view>

      <!-- 公告列表 -->
      <view class="announcements-container">
        <!-- 空状态提示 -->
        <view wx:if="{{announcements.length === 0}}" class="empty-announcements">
          <t-icon name="notification" size="48" color="#ddd" />
          <text class="empty-text">暂无公告，点击上方"新增公告"添加</text>
        </view>

        <!-- 公告列表 -->
        <view wx:else class="announcements-list">
          <view
            wx:for="{{announcements}}"
            wx:key="id"
            wx:for-item="announcement"
            wx:for-index="index"
            class="announcement-wrapper"
          >
            <!-- 公告内容 -->
              <view class="announcement-item">
                <view class="announcement-header">
                  <view class="announcement-order">第{{index + 1}}条</view>
                  <!-- 状态标签改为可点击，用于切换启用/禁用状态 -->
                  <view
                    class="announcement-status {{announcement.isActive ? 'active' : 'inactive'}} clickable"
                    data-index="{{index}}"
                    catchtap="onToggleAnnouncementStatus"
                  >
                    {{announcement.isActive ? '启用' : '禁用'}}
                  </view>
                </view>

                <!-- 非编辑状态：显示为只读文本，点击可进入编辑模式 -->
                <view
                  wx:if="{{announcement.editMode !== true}}"
                  class="announcement-content-display"
                  data-index="{{index}}"
                  catchtap="onEditAnnouncement"
                >
                  <view class="announcement-text-display">{{announcement.content || '请输入公告内容'}}</view>
                </view>

                <!-- 编辑状态：显示为可编辑文本框 -->
                <t-textarea
                  wx:else
                  placeholder="请输入公告内容"
                  value="{{announcement.content}}"
                  bind:change="onAnnouncementContentChange"
                  bind:blur="onAnnouncementContentBlur"
                  data-index="{{index}}"
                  class="announcement-textarea editing"
                  maxlength="500"
                  autosize
                  focus="{{true}}"
                />

                <view class="announcement-meta">
                  <text class="announcement-time">{{announcement.updateTime}}</text>
                  <text class="announcement-length">{{announcement.content.length}}/500</text>
                </view>

                <!-- 编辑模式下的浮动操作按钮 -->
                <view wx:if="{{announcement.editMode === true}}" class="edit-floating-actions">
                  <view
                    class="floating-btn delete-btn"
                    data-index="{{index}}"
                    catchtap="onDeleteAnnouncement"
                  >
                    <t-icon name="delete" size="16" color="#fff" />
                    <text>删除</text>
                  </view>
                  <view
                    class="floating-btn cancel-btn"
                    data-index="{{index}}"
                    catchtap="onCancelEdit"
                  >
                    <t-icon name="close" size="16" color="#666" />
                    <text>取消</text>
                  </view>
                  <view
                    class="floating-btn confirm-btn"
                    data-index="{{index}}"
                    catchtap="onConfirmEdit"
                  >
                    <t-icon name="check" size="16" color="#fff" />
                    <text>确认</text>
                  </view>
                </view>
              </view>
          </view>
        </view>
      </view>

      <view class="setting-description">
        公告将按顺序在首页显示。多条公告时会自动滚动播放，单条公告时静态显示。
      </view>

      <!-- 操作提醒 -->
      <view class="operation-tips">
        <view class="tips-icon">
          <t-icon name="info-circle" size="32" color="#0052D9" />
        </view>
        <view class="tips-content">
          <view class="tips-title">操作提示</view>
          <view class="tips-text">
            • 点击公告内容可直接编辑，失焦自动保存
          </view>
          <view class="tips-text">
            • 点击 <text class="status-demo active">启用</text> / <text class="status-demo inactive">禁用</text> 标签可切换公告状态
          </view>
          <view class="tips-text">
            • 编辑模式下可点击删除按钮移除公告
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据维护设置卡片 -->
  <view class="setting-card">
    <view class="card-header">
      <t-icon name="download" size="24" />
      <text class="card-title">数据维护</text>
    </view>
    <view class="card-body">
      <view class="setting-item">
        <view class="setting-label">数据备份导出</view>
        <view class="setting-description">
          导出全部数据库数据用于备份，支持选择性导出和时间范围过滤
        </view>
      </view>

      <view class="export-actions">
        <t-button
          theme="primary"
          variant="outline"
          size="medium"
          bind:tap="showExportDialog"
          class="export-btn"
        >
          <t-icon name="download" slot="icon" />
          导出数据备份
        </t-button>

        <t-button
          theme="default"
          variant="outline"
          size="medium"
          bind:tap="showExportStatistics"
          class="export-btn"
        >
          <t-icon name="chart" slot="icon" />
          数据统计
        </t-button>
      </view>

      <view class="export-tips">
        <view class="tips-title">导出说明</view>
        <view class="tips-text">• 支持全量导出和选择性导出</view>
        <view class="tips-text">• 支持按时间范围过滤数据</view>
        <view class="tips-text">• 敏感数据可选择脱敏处理</view>
        <view class="tips-text">• 导出文件为JSON格式，便于备份和迁移</view>
      </view>
    </view>
  </view>
  </view> <!-- 结束 content-wrapper -->

  <!-- 数据导出配置弹窗 -->
  <t-dialog
    visible="{{showExportDialog}}"
    title="数据导出配置"
    confirm-btn="开始导出"
    cancel-btn="取消"
    bind:confirm="startExport"
    bind:cancel="closeExportDialog"
    class="export-dialog"
  >
    <view class="export-config">
      <!-- 导出范围选择 -->
      <view class="config-section">
        <view class="section-title">导出范围</view>
        <t-radio-group
          value="{{exportScope}}"
          bind:change="onExportScopeChange"
          class="radio-group"
        >
          <t-radio value="all" class="radio-item">
            <view class="radio-content">
              <text class="radio-title">全部数据</text>
              <text class="radio-desc">导出所有数据库集合</text>
            </view>
          </t-radio>
          <t-radio value="selected" class="radio-item">
            <view class="radio-content">
              <text class="radio-title">选择集合</text>
              <text class="radio-desc">选择特定的数据集合导出</text>
            </view>
          </t-radio>
          <t-radio value="dateRange" class="radio-item">
            <view class="radio-content">
              <text class="radio-title">时间范围</text>
              <text class="radio-desc">按时间范围过滤数据</text>
            </view>
          </t-radio>
        </t-radio-group>
      </view>

      <!-- 集合选择（当选择"选择集合"时显示） -->
      <view class="config-section" wx:if="{{exportScope === 'selected'}}">
        <view class="section-title">选择数据集合</view>
        <t-checkbox-group
          value="{{selectedCollections}}"
          bind:change="onSelectedCollectionsChange"
          class="checkbox-group"
        >
          <t-checkbox
            wx:for="{{availableCollections}}"
            wx:key="id"
            value="{{item.id}}"
            class="checkbox-item"
          >
            <view class="checkbox-content">
              <text class="checkbox-title">{{item.name}}</text>
              <text class="checkbox-desc">{{item.id}}</text>
            </view>
          </t-checkbox>
        </t-checkbox-group>
      </view>

      <!-- 时间范围选择（当选择"时间范围"时显示） -->
      <view class="config-section" wx:if="{{exportScope === 'dateRange'}}">
        <view class="section-title">时间范围</view>
        <view class="date-range">
          <view class="date-item">
            <text class="date-label">开始日期</text>
            <t-input
              type="date"
              value="{{startDate}}"
              bind:change="onStartDateChange"
              placeholder="选择开始日期"
              class="date-input"
            />
          </view>
          <view class="date-item">
            <text class="date-label">结束日期</text>
            <t-input
              type="date"
              value="{{endDate}}"
              bind:change="onEndDateChange"
              placeholder="选择结束日期"
              class="date-input"
            />
          </view>
        </view>
      </view>

      <!-- 导出选项 -->
      <view class="config-section">
        <view class="section-title">导出选项</view>
        <t-checkbox-group
          value="{{exportOptions}}"
          bind:change="onExportOptionsChange"
          class="checkbox-group"
        >
          <t-checkbox value="includeMetadata" class="checkbox-item">
            <view class="checkbox-content">
              <text class="checkbox-title">包含元数据</text>
              <text class="checkbox-desc">导出文件包含统计信息和配置</text>
            </view>
          </t-checkbox>
          <t-checkbox value="privacyMode" class="checkbox-item">
            <view class="checkbox-content">
              <text class="checkbox-title">隐私保护模式</text>
              <text class="checkbox-desc">敏感数据脱敏处理</text>
            </view>
          </t-checkbox>
        </t-checkbox-group>
      </view>
    </view>
  </t-dialog>

  <!-- 导出进度弹窗 -->
  <t-dialog
    visible="{{showExportProgress}}"
    title="正在导出数据"
    show-cancel="{{false}}"
    show-confirm="{{false}}"
    class="progress-dialog"
  >
    <view class="export-progress">
      <t-loading theme="circular" size="48rpx" />
      <view class="progress-text">{{exportStatusText}}</view>
      <view class="progress-detail">{{exportProgressDetail}}</view>
    </view>
  </t-dialog>

  <!-- 数据统计弹窗 -->
  <t-dialog
    visible="{{showStatisticsDialog}}"
    title="数据统计信息"
    confirm-btn="确定"
    show-cancel="{{false}}"
    bind:confirm="closeStatisticsDialog"
    class="statistics-dialog"
  >
    <view class="statistics-content">
      <view class="stats-summary">
        <view class="stats-item">
          <text class="stats-label">数据集合总数</text>
          <text class="stats-value">{{statistics.totalCollections || 0}}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">数据记录总数</text>
          <text class="stats-value">{{statistics.totalRecords || 0}}</text>
        </view>
      </view>

      <view class="stats-details">
        <view class="stats-title">各集合详情</view>
        <view
          wx:for="{{statistics.collections}}"
          wx:key="*this"
          wx:for-item="collection"
          wx:for-index="collectionName"
          class="collection-stat"
        >
          <view class="collection-name">{{collection.name}}</view>
          <view class="collection-count">{{collection.count}} 条记录</view>
        </view>
      </view>
    </view>
  </t-dialog>

  <!-- 提示组件区域 -->
  <t-toast id="t-toast" />
  <t-message id="t-message" />
  <t-dialog id="t-dialog" />
</view>
