<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瑜伽馆数据导出查看器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .upload-area {
            padding: 40px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .upload-box {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 40px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-box:hover {
            border-color: #667eea;
            background: #f8faff;
        }
        
        .upload-box.dragover {
            border-color: #667eea;
            background: #eff6ff;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #9ca3af;
            margin-bottom: 16px;
        }
        
        .upload-text {
            font-size: 18px;
            color: #374151;
            margin-bottom: 8px;
        }
        
        .upload-hint {
            font-size: 14px;
            color: #6b7280;
        }
        
        .file-input {
            display: none;
        }
        
        .content {
            padding: 30px;
        }
        
        .metadata {
            background: #f9fafb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .metadata h3 {
            margin: 0 0 15px 0;
            color: #374151;
            font-size: 18px;
        }
        
        .meta-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .meta-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .meta-label {
            font-weight: 500;
            color: #6b7280;
        }
        
        .meta-value {
            color: #374151;
            font-weight: 600;
        }
        
        .collections {
            margin-top: 30px;
        }
        
        .collection-item {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .collection-header {
            background: #f9fafb;
            padding: 15px 20px;
            border-bottom: 1px solid #e5e7eb;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .collection-header:hover {
            background: #f3f4f6;
        }
        
        .collection-title {
            font-weight: 600;
            color: #374151;
            font-size: 16px;
        }
        
        .collection-count {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .collection-content {
            padding: 20px;
            display: none;
        }
        
        .collection-content.expanded {
            display: block;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        
        .data-table th,
        .data-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .data-table td {
            color: #6b7280;
        }
        
        .json-viewer {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #16a34a;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn-secondary {
            background: #6b7280;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧘‍♀️ 瑜伽馆数据导出查看器</h1>
            <p>上传导出的JSON文件来查看和分析数据</p>
        </div>
        
        <div class="upload-area">
            <div class="upload-box" id="uploadBox">
                <div class="upload-icon">📁</div>
                <div class="upload-text">点击选择文件或拖拽文件到此处</div>
                <div class="upload-hint">支持 .json 格式的导出文件</div>
                <input type="file" class="file-input" id="fileInput" accept=".json">
            </div>
        </div>
        
        <div class="content" id="content" style="display: none;">
            <div class="metadata" id="metadata"></div>
            <div class="collections" id="collections"></div>
        </div>
    </div>

    <script>
        const uploadBox = document.getElementById('uploadBox');
        const fileInput = document.getElementById('fileInput');
        const content = document.getElementById('content');
        const metadata = document.getElementById('metadata');
        const collections = document.getElementById('collections');

        // 点击上传区域
        uploadBox.addEventListener('click', () => {
            fileInput.click();
        });

        // 文件选择
        fileInput.addEventListener('change', handleFile);

        // 拖拽上传
        uploadBox.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadBox.classList.add('dragover');
        });

        uploadBox.addEventListener('dragleave', () => {
            uploadBox.classList.remove('dragover');
        });

        uploadBox.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadBox.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileContent(files[0]);
            }
        });

        function handleFile(e) {
            const file = e.target.files[0];
            if (file) {
                handleFileContent(file);
            }
        }

        function handleFileContent(file) {
            if (!file.name.endsWith('.json')) {
                showError('请选择 JSON 格式的文件');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    displayData(data);
                    showSuccess(`文件 "${file.name}" 加载成功`);
                } catch (error) {
                    showError('文件格式错误，请确保是有效的 JSON 文件');
                }
            };
            reader.readAsText(file);
        }

        function displayData(data) {
            content.style.display = 'block';
            
            // 显示元数据
            displayMetadata(data);
            
            // 显示集合数据
            displayCollections(data.collections || {});
        }

        function displayMetadata(data) {
            const metaHtml = `
                <h3>📊 导出信息</h3>
                <div class="meta-grid">
                    <div class="meta-item">
                        <span class="meta-label">导出时间</span>
                        <span class="meta-value">${new Date(data.exportTime).toLocaleString()}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">版本</span>
                        <span class="meta-value">${data.version || 'N/A'}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">格式</span>
                        <span class="meta-value">${data.format || 'JSON'}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">集合数量</span>
                        <span class="meta-value">${Object.keys(data.collections || {}).length}</span>
                    </div>
                    ${data.metadata ? `
                    <div class="meta-item">
                        <span class="meta-label">总记录数</span>
                        <span class="meta-value">${data.metadata.totalRecords || 'N/A'}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">隐私模式</span>
                        <span class="meta-value">${data.metadata.privacyMode ? '是' : '否'}</span>
                    </div>
                    ` : ''}
                </div>
            `;
            metadata.innerHTML = metaHtml;
        }

        function displayCollections(collectionsData) {
            let html = '<h3>📚 数据集合</h3>';
            
            Object.entries(collectionsData).forEach(([key, collection]) => {
                const count = collection.count || (collection.data ? collection.data.length : 0);
                html += `
                    <div class="collection-item">
                        <div class="collection-header" onclick="toggleCollection('${key}')">
                            <span class="collection-title">${collection.name || key}</span>
                            <span class="collection-count">${count} 条记录</span>
                        </div>
                        <div class="collection-content" id="collection-${key}">
                            ${collection.error ? 
                                `<div class="error">导出错误: ${collection.error}</div>` :
                                generateTableHtml(collection.data || [])
                            }
                        </div>
                    </div>
                `;
            });
            
            collections.innerHTML = html;
        }

        function generateTableHtml(data) {
            if (!data || data.length === 0) {
                return '<p style="color: #6b7280; text-align: center; padding: 20px;">暂无数据</p>';
            }

            const firstItem = data[0];
            const headers = Object.keys(firstItem);
            
            let html = '<table class="data-table"><thead><tr>';
            headers.forEach(header => {
                html += `<th>${header}</th>`;
            });
            html += '</tr></thead><tbody>';
            
            data.slice(0, 10).forEach(item => { // 只显示前10条记录
                html += '<tr>';
                headers.forEach(header => {
                    let value = item[header];
                    if (typeof value === 'object') {
                        value = JSON.stringify(value);
                    }
                    if (typeof value === 'string' && value.length > 50) {
                        value = value.substring(0, 50) + '...';
                    }
                    html += `<td>${value || ''}</td>`;
                });
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            
            if (data.length > 10) {
                html += `<p style="color: #6b7280; text-align: center; margin-top: 15px;">显示前 10 条记录，共 ${data.length} 条</p>`;
            }
            
            return html;
        }

        function toggleCollection(key) {
            const element = document.getElementById(`collection-${key}`);
            element.classList.toggle('expanded');
        }

        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            content.insertBefore(errorDiv, content.firstChild);
            setTimeout(() => errorDiv.remove(), 5000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            content.insertBefore(successDiv, content.firstChild);
            setTimeout(() => successDiv.remove(), 3000);
        }
    </script>
</body>
</html>
