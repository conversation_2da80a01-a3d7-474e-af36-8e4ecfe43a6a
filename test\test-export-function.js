// test-export-function.js
// 数据导出云函数测试脚本
// 用于在开发环境中测试导出功能

/**
 * 测试数据导出功能
 * 
 * 使用方法：
 * 1. 在微信开发者工具的控制台中运行此脚本
 * 2. 或者在小程序页面中调用这些测试函数
 */

// 测试配置
const TEST_CONFIG = {
  // 测试用的管理员openid（需要替换为实际的管理员openid）
  adminOpenid: 'test_admin_openid',
  
  // 云函数名称
  cloudFunctionName: 'dataExport',
  
  // 测试超时时间（毫秒）
  timeout: 30000
};

/**
 * 测试获取集合列表
 */
async function testGetCollectionsList() {
  console.log('🧪 测试获取集合列表...');
  
  try {
    const result = await wx.cloud.callFunction({
      name: TEST_CONFIG.cloudFunctionName,
      data: {
        action: 'getCollectionsList'
      }
    });
    
    console.log('✅ 获取集合列表成功:', result.result);
    
    if (result.result.success) {
      const collections = result.result.data.collections;
      console.log(`📊 共找到 ${collections.length} 个数据集合:`);
      collections.forEach(collection => {
        console.log(`  - ${collection.name} (${collection.id})`);
      });
    }
    
    return result.result;
    
  } catch (error) {
    console.error('❌ 获取集合列表失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试获取数据统计
 */
async function testGetExportStatistics() {
  console.log('🧪 测试获取数据统计...');
  
  try {
    const result = await wx.cloud.callFunction({
      name: TEST_CONFIG.cloudFunctionName,
      data: {
        action: 'getExportStatistics'
      }
    });
    
    console.log('✅ 获取数据统计成功:', result.result);
    
    if (result.result.success) {
      const stats = result.result.data;
      console.log(`📊 数据统计信息:`);
      console.log(`  - 总集合数: ${stats.totalCollections}`);
      console.log(`  - 总记录数: ${stats.totalRecords}`);
      console.log(`  - 各集合详情:`);
      
      Object.entries(stats.collections).forEach(([key, collection]) => {
        console.log(`    ${collection.name}: ${collection.count} 条记录`);
      });
    }
    
    return result.result;
    
  } catch (error) {
    console.error('❌ 获取数据统计失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试选择性导出（小数据量）
 */
async function testSelectedCollectionsExport() {
  console.log('🧪 测试选择性导出...');
  
  try {
    const result = await wx.cloud.callFunction({
      name: TEST_CONFIG.cloudFunctionName,
      data: {
        action: 'exportSelectedCollections',
        data: {
          collections: ['systemSettings'], // 只导出系统设置，数据量小
          format: 'json',
          includeMetadata: true,
          privacyMode: true
        }
      }
    });
    
    console.log('✅ 选择性导出成功:', result.result);
    
    if (result.result.success) {
      const data = result.result.data;
      console.log(`📁 导出文件信息:`);
      console.log(`  - 文件名: ${data.fileName}`);
      console.log(`  - 记录数: ${data.totalRecords}`);
      console.log(`  - 文件大小: ${data.fileSize}`);
      console.log(`  - 导出时间: ${new Date(data.exportTime).toLocaleString()}`);
      
      // 显示导出数据的结构（不显示具体内容，避免日志过长）
      const collections = Object.keys(data.exportData.collections);
      console.log(`  - 包含集合: ${collections.join(', ')}`);
    }
    
    return result.result;
    
  } catch (error) {
    console.error('❌ 选择性导出失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试时间范围导出
 */
async function testDateRangeExport() {
  console.log('🧪 测试时间范围导出...');
  
  // 设置测试时间范围（最近7天）
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 7);
  
  try {
    const result = await wx.cloud.callFunction({
      name: TEST_CONFIG.cloudFunctionName,
      data: {
        action: 'exportByDateRange',
        data: {
          collections: ['systemSettings', 'notifications'], // 选择数据量较小的集合
          dateRange: {
            start: startDate.toISOString().split('T')[0],
            end: endDate.toISOString().split('T')[0]
          },
          format: 'json',
          includeMetadata: true,
          privacyMode: true
        }
      }
    });
    
    console.log('✅ 时间范围导出成功:', result.result);
    
    if (result.result.success) {
      const data = result.result.data;
      console.log(`📁 导出文件信息:`);
      console.log(`  - 文件名: ${data.fileName}`);
      console.log(`  - 记录数: ${data.totalRecords}`);
      console.log(`  - 文件大小: ${data.fileSize}`);
      console.log(`  - 时间范围: ${data.dateRange.start} 到 ${data.dateRange.end}`);
    }
    
    return result.result;
    
  } catch (error) {
    console.error('❌ 时间范围导出失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试权限验证
 */
async function testPermissionValidation() {
  console.log('🧪 测试权限验证...');
  
  try {
    // 尝试在没有管理员权限的情况下调用导出功能
    const result = await wx.cloud.callFunction({
      name: TEST_CONFIG.cloudFunctionName,
      data: {
        action: 'exportFullDatabase',
        data: {
          format: 'json'
        }
      }
    });
    
    console.log('🔒 权限验证结果:', result.result);
    
    if (!result.result.success && result.result.message.includes('权限')) {
      console.log('✅ 权限验证正常工作');
    } else if (result.result.success) {
      console.log('✅ 当前用户具有管理员权限');
    } else {
      console.log('⚠️ 权限验证结果异常');
    }
    
    return result.result;
    
  } catch (error) {
    console.error('❌ 权限验证测试失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始运行数据导出功能测试套件...');
  console.log('='.repeat(50));
  
  const tests = [
    { name: '获取集合列表', func: testGetCollectionsList },
    { name: '获取数据统计', func: testGetExportStatistics },
    { name: '权限验证', func: testPermissionValidation },
    { name: '选择性导出', func: testSelectedCollectionsExport },
    { name: '时间范围导出', func: testDateRangeExport }
  ];
  
  const results = [];
  
  for (const test of tests) {
    console.log(`\n📋 运行测试: ${test.name}`);
    console.log('-'.repeat(30));
    
    try {
      const startTime = Date.now();
      const result = await test.func();
      const duration = Date.now() - startTime;
      
      results.push({
        name: test.name,
        success: result.success,
        duration: duration,
        message: result.message || result.error
      });
      
      console.log(`⏱️ 耗时: ${duration}ms`);
      
    } catch (error) {
      results.push({
        name: test.name,
        success: false,
        duration: 0,
        message: error.message
      });
      
      console.error(`💥 测试异常:`, error);
    }
  }
  
  // 输出测试总结
  console.log('\n' + '='.repeat(50));
  console.log('📊 测试结果总结:');
  console.log('='.repeat(50));
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    const duration = result.duration > 0 ? ` (${result.duration}ms)` : '';
    console.log(`${status} ${result.name}${duration}`);
    if (!result.success && result.message) {
      console.log(`   错误: ${result.message}`);
    }
  });
  
  console.log('-'.repeat(50));
  console.log(`🎯 测试通过率: ${successCount}/${totalCount} (${Math.round(successCount/totalCount*100)}%)`);
  
  if (successCount === totalCount) {
    console.log('🎉 所有测试通过！数据导出功能工作正常。');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能。');
  }
  
  return results;
}

/**
 * 快速测试（仅测试基础功能）
 */
async function quickTest() {
  console.log('⚡ 运行快速测试...');
  
  try {
    // 测试云函数连接
    const result = await testGetCollectionsList();
    
    if (result.success) {
      console.log('✅ 快速测试通过：云函数连接正常');
      return true;
    } else {
      console.log('❌ 快速测试失败：云函数连接异常');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 快速测试异常:', error);
    return false;
  }
}

// 导出测试函数，供外部调用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    quickTest,
    testGetCollectionsList,
    testGetExportStatistics,
    testSelectedCollectionsExport,
    testDateRangeExport,
    testPermissionValidation
  };
}

// 在控制台中可以直接调用的全局函数
if (typeof window !== 'undefined') {
  window.testDataExport = {
    runAllTests,
    quickTest,
    testGetCollectionsList,
    testGetExportStatistics,
    testSelectedCollectionsExport,
    testDateRangeExport,
    testPermissionValidation
  };
  
  console.log('🔧 数据导出测试工具已加载');
  console.log('💡 使用方法:');
  console.log('  - testDataExport.quickTest() // 快速测试');
  console.log('  - testDataExport.runAllTests() // 完整测试');
  console.log('  - testDataExport.testGetCollectionsList() // 测试获取集合列表');
}
